<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Support\Facades\Auth;

class DashboardController extends Controller
{
    /**
     * Admin Dashboard
     */
    public function adminDashboard(): View
    {
        $user = Auth::user();

        // Get admin dashboard statistics
        $stats = [
            'total_users' => \App\Models\User::count(),
            'total_sellers' => \App\Models\Seller::count(),
            'pending_sellers' => \App\Models\Seller::where('verification_status', 'pending')->count(),
            'total_products' => \App\Models\Product::count(),
            'total_orders' => \App\Models\Order::count(),
            'pending_disputes' => \App\Models\Dispute::where('status', 'open')->count(),
        ];

        return view('admin.dashboard', compact('user', 'stats'));
    }

    /**
     * Seller Dashboard
     */
    public function sellerDashboard(): View
    {
        $user = Auth::user();
        $seller = $user->seller;

        // Get seller dashboard statistics
        $stats = [
            'total_products' => $user->products()->count(),
            'active_products' => $user->products()->where('status', 'active')->count(),
            'total_orders' => $seller->orders()->count(),
            'pending_orders' => $seller->orders()->where('status', 'in_progress')->count(),
            'total_earnings' => $seller->total_earnings,
            'pending_earnings' => $seller->pending_earnings,
            'available_balance' => $seller->available_balance,
            'average_rating' => $seller->average_rating,
        ];

        // Recent orders
        $recentOrders = $seller->orders()
            ->with(['product', 'buyer'])
            ->latest()
            ->take(5)
            ->get();

        return view('seller.dashboard', compact('user', 'seller', 'stats', 'recentOrders'));
    }

    /**
     * Buyer Dashboard
     */
    public function buyerDashboard(): View
    {
        $user = Auth::user();

        // Get buyer dashboard statistics
        $stats = [
            'total_orders' => $user->buyerOrders()->count(),
            'active_orders' => $user->buyerOrders()->whereIn('status', ['paid', 'in_progress'])->count(),
            'completed_orders' => $user->buyerOrders()->where('status', 'completed')->count(),
            'total_spent' => $user->buyerOrders()->where('status', 'completed')->sum('amount'),
        ];

        // Recent orders
        $recentOrders = $user->buyerOrders()
            ->with(['product', 'seller'])
            ->latest()
            ->take(5)
            ->get();

        // Recommended products (simple logic for now)
        $recommendedProducts = \App\Models\Product::active()
            ->with(['seller', 'category'])
            ->inRandomOrder()
            ->take(6)
            ->get();

        return view('buyer.dashboard', compact('user', 'stats', 'recentOrders', 'recommendedProducts'));
    }
}
