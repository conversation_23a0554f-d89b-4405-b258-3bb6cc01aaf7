<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Auth\LoginController;
use App\Http\Controllers\Auth\RegisterController;
use App\Http\Controllers\DashboardController;

// Home page
Route::get('/', function () {
    return view('home');
})->name('home');

// Authentication Routes
Route::middleware('guest')->group(function () {
    // Login
    Route::get('/login', [LoginController::class, 'showLoginForm'])->name('login');
    Route::post('/login', [LoginController::class, 'login']);

    // Registration
    Route::get('/register', [RegisterController::class, 'showRegistrationForm'])->name('register');
    Route::get('/register/buyer', [RegisterController::class, 'showBuyerRegistrationForm'])->name('register.buyer');
    Route::get('/register/seller', [RegisterController::class, 'showSellerRegistrationForm'])->name('register.seller');
    Route::post('/register/buyer', [RegisterController::class, 'registerBuyer'])->name('register.buyer.submit');
    Route::post('/register/seller', [RegisterController::class, 'registerSeller'])->name('register.seller.submit');
});

// Logout (authenticated users only)
Route::post('/logout', [LoginController::class, 'logout'])->name('logout')->middleware('auth');

// Dashboard Routes (Role-based)
Route::middleware(['auth'])->group(function () {
    // Admin Dashboard
    Route::middleware(['role:admin'])->prefix('admin')->name('admin.')->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'adminDashboard'])->name('dashboard');
    });

    // Seller Dashboard
    Route::middleware(['role:seller'])->prefix('seller')->name('seller.')->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'sellerDashboard'])->name('dashboard');

        Route::get('/verification', function () {
            return view('seller.verification');
        })->name('verification');
    });

    // Buyer Dashboard
    Route::middleware(['role:buyer'])->prefix('buyer')->name('buyer.')->group(function () {
        Route::get('/dashboard', [DashboardController::class, 'buyerDashboard'])->name('dashboard');
    });
});
